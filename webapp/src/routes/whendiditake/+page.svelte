<script lang="ts">
  import Navigation from '$lib/components/Navigation.svelte';
  import QuickTakeOption from '$lib/components/QuickTakeOption.svelte';
  import TakeRecord from '$lib/components/TakeRecord.svelte';
  import { onMount, onDestroy } from 'svelte';
  import { flip } from 'svelte/animate';
  import { deletePharmaceuticalTake, updatePharmaceuticalTake } from '$lib/pharmaceuticalActions';
  import { 
    quickPharmaceuticalOptions, 
    pharmaceuticalTakeRecords, 
    isLoadingMore,
    hasMoreData,
    loadMorePharmaceuticalTakeRecords,
    type PharmaceuticalTakeRecord as PharmaceuticalTakeRecordType
  } from '$lib';
  import { dataManager } from '$lib/services/dataManager';
  import { globalLoadingStore, takeRecordsStore, pharmaceuticalsStore } from '$lib/stores/dataStore';
  import { LoadingState, shouldShowLoader, shouldShowSkeleton } from '$lib/types/loadingTypes';
  import LoadingStateComponent from '$lib/components/LoadingState.svelte';

  let showScrollToTop = false;
  let scrollContainer: HTMLElement;
  let errorMessage = '';
  let showError = false;
  
  // 刪除確認對話框
  let showDeleteConfirm = false;
  let recordToDelete: PharmaceuticalTakeRecordType | null = null;
  let isDeleting = false;
  let deletingRecordIds = new Set<number>();
  
  // 編輯對話框
  let showEditDialog = false;
  let recordToEdit: PharmaceuticalTakeRecordType | null = null;
  let isEditing = false;
  let editForm = {
    quantity: 0,
    takenAt: '',
    notes: ''
  };
  
  function handleTimeChange(e: Event) {
     if (recordToEdit && e.target) {
       const target = e.target as HTMLInputElement;
       if (target.value) {
         const [date, time] = target.value.split('T');
         const originalDate = new Date(recordToEdit.takenAt);
         const originalTimeString = originalDate.toTimeString();
         
         // 如果只修改了日期，保留原始時間
         if (time === undefined) {
           const [hours, minutes] = originalTimeString.split(':');
           editForm.takenAt = `${date}T${hours}:${minutes}:00.000Z`;
         } else {
           editForm.takenAt = `${date}T${time}:00.000Z`;
         }
       }
     }
   }

  // 監聽滾動事件
  function handleScroll() {
    if (!scrollContainer) return;
    
    const { scrollTop, scrollHeight, clientHeight } = scrollContainer;
    
    // 顯示/隱藏回到頂部按鈕
    showScrollToTop = scrollTop > 300;
    
    // 檢查是否需要載入更多資料
    if (scrollTop + clientHeight >= scrollHeight - 100 && !$isLoadingMore && $hasMoreData) {
      loadMoreRecords();
    }
  }

  // 載入更多歷史紀錄
  async function loadMoreRecords() {
    if ($isLoadingMore || !$hasMoreData) return;
    
    try {
      await loadMorePharmaceuticalTakeRecords();
    } catch (error) {
      console.error('載入更多紀錄失敗:', error);
    }
  }

  // 回到頂部
  function scrollToTop() {
    if (scrollContainer) {
      scrollContainer.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }

  // 處理快速用藥新增
  function handleQuickPharmaceuticalAdded(event: CustomEvent) {
    console.log('快速用藥已新增:', event.detail);
  }

  function handleQuickPharmaceuticalError(event: CustomEvent) {
    console.error('快速用藥失敗:', event.detail.error);
    showErrorMessage(event.detail.error);
  }

  function showErrorMessage(message: string) {
    errorMessage = message;
    showError = true;
    // 3秒後自動隱藏錯誤提示
    setTimeout(() => {
      showError = false;
    }, 5000);
  }

  function hideError() {
    showError = false;
  }

  // 處理用藥紀錄編輯和刪除
  function handlePharmaceuticalEdit(event: CustomEvent) {
    recordToEdit = event.detail;
    if (recordToEdit) {
      editForm = {
        quantity: recordToEdit.dosage,
        takenAt: recordToEdit.takenAt,
        notes: recordToEdit.notes || ''
      };
      showEditDialog = true;
    }
  }

  function handlePharmaceuticalDelete(event: CustomEvent) {
    recordToDelete = event.detail;
    showDeleteConfirm = true;
  }

  // 確認刪除
  async function confirmDelete() {
    if (!recordToDelete) return;
    
    try {
      isDeleting = true;
      const recordIdToDelete = recordToDelete.id;
      deletingRecordIds.add(recordIdToDelete);
      deletingRecordIds = deletingRecordIds; // 觸發反應性更新
      
      // 關閉對話框
      showDeleteConfirm = false;
      recordToDelete = null;
      
      // 先標記為刪除中，讓動畫開始
      await new Promise(resolve => setTimeout(resolve, 50));
      
      // 執行刪除操作
      const success = await deletePharmaceuticalTake(recordIdToDelete);
      
      // 等待動畫完成
      await new Promise(resolve => setTimeout(resolve, 300));
      
      if (success) {
        showErrorMessage('用藥紀錄已成功刪除');
      } else {
        showErrorMessage('刪除失敗，請稍後重試');
        // 如果刪除失敗，移除刪除標記
        deletingRecordIds.delete(recordIdToDelete);
        deletingRecordIds = deletingRecordIds;
      }
    } catch (error) {
      console.error('刪除用藥紀錄失敗:', error);
      showErrorMessage(error instanceof Error ? error.message : '刪除失敗，請稍後重試');
    } finally {
      isDeleting = false;
      // 等待一段時間後清除刪除狀態
      setTimeout(() => {
        deletingRecordIds.clear();
        deletingRecordIds = deletingRecordIds;
      }, 400);
    }
  }

  // 取消刪除
  function cancelDelete() {
    showDeleteConfirm = false;
    recordToDelete = null;
  }

  // 確認編輯
  async function confirmEdit() {
    if (!recordToEdit) return;
    
    try {
      isEditing = true;
      
      const updateData = {
        id: recordToEdit.id,
        quantity: editForm.quantity,
        takenAt: editForm.takenAt,
        notes: editForm.notes || undefined
      };
      
      await updatePharmaceuticalTake(updateData);
      
      showEditDialog = false;
      recordToEdit = null;
      showErrorMessage('用藥紀錄已成功更新');
    } catch (error) {
      console.error('更新用藥紀錄失敗:', error);
      showErrorMessage(error instanceof Error ? error.message : '更新失敗，請稍後重試');
    } finally {
      isEditing = false;
    }
  }

  // 取消編輯
  function cancelEdit() {
    showEditDialog = false;
    recordToEdit = null;
  }

  onMount(async () => {
    // 檢查並載入資料（如果尚未載入）
    try {
      console.log('=== 主頁面載入開始 ===');
      console.log('藥物清單狀態:', $pharmaceuticalsStore);
      console.log('服藥記錄狀態:', $takeRecordsStore);
      console.log('快速選項:', $quickPharmaceuticalOptions);
      
      // 如果 DataManager 還沒有載入資料，則載入
      if (!$pharmaceuticalsStore.data && $pharmaceuticalsStore.loading.state === LoadingState.NOT_LOADED) {
        console.log('開始載入藥物清單...');
        await dataManager.loadPharmaceuticals();
        console.log('藥物清單載入完成:', $pharmaceuticalsStore.data);
      }
      if (!$takeRecordsStore.data && $takeRecordsStore.loading.state === LoadingState.NOT_LOADED) {
        console.log('開始載入服藥記錄...');
        await dataManager.loadTakeRecords();
        console.log('服藥記錄載入完成:', $takeRecordsStore.data);
      }
      
      console.log('載入後的快速選項:', $quickPharmaceuticalOptions);
      
      // 強制觸發快速選項生成（如果還沒有的話）
      if ($quickPharmaceuticalOptions.length === 0 && $pharmaceuticalTakeRecords.length > 0) {
        console.log('手動觸發快速選項生成...');
        import('$lib/pharmaceuticalStore').then(({ generateQuickPharmaceuticalOptionsSync, quickPharmaceuticalOptions }) => {
          const quickOptions = generateQuickPharmaceuticalOptionsSync($pharmaceuticalTakeRecords);
          quickPharmaceuticalOptions.set(quickOptions);
          console.log('手動生成的快速選項:', quickOptions);
        });
      }
      
      console.log('=== 主頁面載入完成 ===');
    } catch (error) {
      console.error('載入初始資料失敗:', error);
      errorMessage = `載入資料失敗: ${error.message}`;
      showError = true;
    }
  });

  onDestroy(() => {
    // 清理事件監聽器
  });

  // 根據日期分組的用藥紀錄
  $: groupedRecords = $pharmaceuticalTakeRecords.reduce((groups, record) => {
    const date = new Date(record.takenAt).toLocaleDateString('zh-TW');
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(record);
    return groups;
  }, {} as Record<string, PharmaceuticalTakeRecordType[]>);

  // 按日期排序的群組鍵
  $: sortedDateKeys = Object.keys(groupedRecords).sort((a, b) => 
    new Date(b.split('/').reverse().join('-')).getTime() - 
    new Date(a.split('/').reverse().join('-')).getTime()
  );
</script>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
  <!-- 固定導航列 -->
  <div class="sticky top-0 z-50">
    <Navigation />
  </div>

  <!-- 錯誤提示 -->
  {#if showError}
    <div class="fixed top-20 left-1/2 transform -translate-x-1/2 z-40 max-w-md w-full mx-4">
      <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg shadow-lg flex items-center justify-between">
        <div class="flex items-center space-x-2">
          <iconify-icon icon="lucide:alert-circle" class="text-lg"></iconify-icon>
          <span class="text-sm font-medium">{errorMessage}</span>
        </div>
        <button 
          on:click={hideError}
          class="text-red-500 hover:text-red-700 ml-2"
        >
          <iconify-icon icon="lucide:x" class="text-lg"></iconify-icon>
        </button>
      </div>
    </div>
  {/if}

  <!-- 主要內容區域 -->
  <div 
    class="h-screen overflow-y-auto"
    bind:this={scrollContainer}
    on:scroll={handleScroll}
  >
    <main class="container mx-auto px-4 pt-20 pb-6 space-y-6">
      <!-- 快速新增區塊 -->
      <section class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="p-6">
          <div class="flex items-center space-x-2 mb-4">
            <iconify-icon icon="lucide:zap" class="text-xl text-green-500"></iconify-icon>
            <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100">快速服藥</h2>
          </div>
          
          {#if shouldShowLoader($pharmaceuticalsStore.loading.state) || shouldShowSkeleton($pharmaceuticalsStore.loading.state)}
            <LoadingStateComponent 
              state={$pharmaceuticalsStore.loading.state} 
              error={$pharmaceuticalsStore.loading.error}
              skeletonType="card"
              itemCount={3}
              retryCallback={() => dataManager.loadPharmaceuticals(true)}
            />
          {:else if $quickPharmaceuticalOptions.length === 0}
            <div class="text-center py-8 text-gray-500 dark:text-gray-400">
              <iconify-icon icon="lucide:pill" class="text-3xl mb-2"></iconify-icon>
              <p>尚無快速服藥選項</p>
              <p class="text-sm mt-1">建立一些用藥紀錄後，系統會自動生成常用的快速選項</p>
            </div>
          {:else}
            <div class="grid gap-3 md:grid-cols-2">
              {#each $quickPharmaceuticalOptions as option}
                <QuickTakeOption 
                  {option}
                  on:added={handleQuickPharmaceuticalAdded}
                  on:error={handleQuickPharmaceuticalError}
                />
              {/each}
            </div>
          {/if}
        </div>
      </section>

      <!-- 用藥紀錄區塊 -->
      <section class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="p-6">
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-2">
              <iconify-icon icon="lucide:history" class="text-xl text-blue-500"></iconify-icon>
              <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100">用藥紀錄</h2>
            </div>
            <span class="text-sm text-gray-500 dark:text-gray-400">
              {$pharmaceuticalTakeRecords.length} 筆紀錄
            </span>
          </div>

          {#if shouldShowLoader($takeRecordsStore.loading.state) || shouldShowSkeleton($takeRecordsStore.loading.state)}
            <LoadingStateComponent 
              state={$takeRecordsStore.loading.state} 
              error={$takeRecordsStore.loading.error}
              skeletonType="list"
              itemCount={5}
              retryCallback={() => dataManager.loadTakeRecords(true)}
            />
          {:else if $pharmaceuticalTakeRecords.length === 0}
            <div class="text-center py-12 text-gray-500 dark:text-gray-400">
              <iconify-icon icon="lucide:calendar-x" class="text-4xl mb-3"></iconify-icon>
              <p class="text-lg mb-2">尚無用藥紀錄</p>
              <p class="text-sm">開始記錄您的用藥情況吧！</p>
            </div>
          {:else}
            <div class="space-y-6">
              {#each sortedDateKeys as dateKey}
                <div class="space-y-3">
                  <!-- 日期標題 -->
                  <div class="flex items-center space-x-2 py-2 border-b border-gray-200 dark:border-gray-600">
                    <iconify-icon icon="lucide:calendar" class="text-sm text-gray-400"></iconify-icon>
                    <h3 class="font-medium text-gray-700 dark:text-gray-300">{dateKey}</h3>
                    <span class="text-xs text-gray-400">({groupedRecords[dateKey].length} 次)</span>
                  </div>
                  
                  <!-- 該日期的用藥紀錄 -->
                  <div class="space-y-2">
                    {#each groupedRecords[dateKey].sort((a, b) => new Date(b.takenAt).getTime() - new Date(a.takenAt).getTime()) as record (record.id)}
                      <div animate:flip={{ duration: 300 }}>
                        <TakeRecord 
                          {record}
                          isDeleting={deletingRecordIds.has(record.id)}
                          on:edit={handlePharmaceuticalEdit}
                          on:delete={handlePharmaceuticalDelete}
                        />
                      </div>
                    {/each}
                  </div>
                </div>
              {/each}

              <!-- 載入更多指示器 -->
              {#if $isLoadingMore}
                <div class="flex items-center justify-center py-6">
                  <iconify-icon icon="lucide:loader-2" class="text-xl text-gray-400 animate-spin"></iconify-icon>
                  <span class="ml-2 text-gray-500">載入更多...</span>
                </div>
              {:else if !$hasMoreData}
                <div class="text-center py-6 text-gray-400 dark:text-gray-500">
                  <iconify-icon icon="lucide:check-circle" class="text-xl"></iconify-icon>
                  <p class="mt-2 text-sm">已載入所有紀錄</p>
                </div>
              {/if}
            </div>
          {/if}
        </div>
      </section>
    </main>
  </div>

  <!-- 回到頂部按鈕 -->
  {#if showScrollToTop}
    <button
      class="fixed bottom-6 right-6 z-40 test-btn !bg-blue-600 hover:!bg-blue-700 !p-3 rounded-full shadow-lg transition-all duration-300 transform hover:scale-110"
      on:click={scrollToTop}
      aria-label="回到頂部"
    >
      <iconify-icon icon="lucide:arrow-up" class="text-xl"></iconify-icon>
    </button>
  {/if}
</div>

<!-- 刪除確認對話框 -->
{#if showDeleteConfirm && recordToDelete}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" role="dialog" aria-labelledby="delete-title">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 p-6">
      <div class="flex items-center space-x-3 mb-4">
        <div class="flex-shrink-0">
          <iconify-icon icon="lucide:trash-2" class="text-2xl text-red-500"></iconify-icon>
        </div>
        <div>
          <h3 id="delete-title" class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            確認刪除用藥紀錄
          </h3>
        </div>
      </div>
      
      <div class="mb-6">
        <p class="text-gray-600 dark:text-gray-300 mb-3">
          您確定要刪除這筆用藥紀錄嗎？
        </p>
        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 space-y-2">
          <p class="text-sm"><strong>藥物：</strong>{recordToDelete.pharmaceuticalName}</p>
          <p class="text-sm"><strong>劑量：</strong>{recordToDelete.dosage} {recordToDelete.unit}</p>
          <p class="text-sm"><strong>服用時間：</strong>{new Date(recordToDelete.takenAt).toLocaleString('zh-TW')}</p>
          {#if recordToDelete.notes}
            <p class="text-sm"><strong>註記：</strong>{recordToDelete.notes}</p>
          {/if}
        </div>
        <p class="text-sm text-red-600 dark:text-red-400 mt-2">
          此操作無法復原。
        </p>
      </div>

      <div class="flex space-x-3 justify-end">
        <button
          type="button"
          class="test-btn !bg-gray-500 hover:!bg-gray-600 !px-4 !py-2"
          on:click={cancelDelete}
          disabled={isDeleting}
        >
          取消
        </button>
        <button
          type="button"
          class="test-btn !bg-red-600 hover:!bg-red-700 !px-4 !py-2 flex items-center space-x-2"
          on:click={confirmDelete}
          disabled={isDeleting}
        >
          {#if isDeleting}
            <iconify-icon icon="lucide:loader-2" class="text-sm animate-spin"></iconify-icon>
            <span>刪除中...</span>
          {:else}
            <iconify-icon icon="lucide:trash-2" class="text-sm"></iconify-icon>
            <span>確認刪除</span>
          {/if}
        </button>
      </div>
    </div>
  </div>
{/if}

<!-- 編輯對話框 -->
{#if showEditDialog && recordToEdit}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" role="dialog" aria-labelledby="edit-title">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 p-6">
      <div class="flex items-center space-x-3 mb-4">
        <div class="flex-shrink-0">
          <iconify-icon icon="lucide:edit-2" class="text-2xl text-blue-500"></iconify-icon>
        </div>
        <div>
          <h3 id="edit-title" class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            編輯用藥紀錄
          </h3>
        </div>
      </div>
      
      <div class="space-y-4 mb-6">
        <!-- 藥物名稱（只讀） -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            藥物名稱
          </label>
          <div class="p-3 bg-gray-100 dark:bg-gray-700 rounded-lg text-gray-600 dark:text-gray-300">
            {recordToEdit.pharmaceuticalName}
          </div>
        </div>

        <!-- 劑量 -->
        <div>
          <label for="edit-quantity" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            劑量
          </label>
          <div class="flex items-center space-x-2">
            <input
              id="edit-quantity"
              type="number"
              min="0.1"
              step="0.1"
              bind:value={editForm.quantity}
              class="flex-1 p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              disabled={isEditing}
            />
            <span class="text-gray-600 dark:text-gray-400">{recordToEdit.unit}</span>
          </div>
        </div>

        <!-- 服用時間 -->
        <div>
          <label for="edit-taken-at" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            服用時間
          </label>
          <input
              id="edit-taken-at"
              type="datetime-local"
              value={editForm.takenAt ? editForm.takenAt.slice(0, 16) : ''}
              on:input={handleTimeChange}
              class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              disabled={isEditing}
            />
        </div>

        <!-- 註記 -->
        <div>
          <label for="edit-notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            註記
          </label>
          <textarea
            id="edit-notes"
            bind:value={editForm.notes}
            rows="3"
            class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            placeholder="選填..."
            disabled={isEditing}
          ></textarea>
        </div>
      </div>

      <div class="flex space-x-3 justify-end">
        <button
          type="button"
          class="test-btn !bg-gray-500 hover:!bg-gray-600 !px-4 !py-2"
          on:click={cancelEdit}
          disabled={isEditing}
        >
          取消
        </button>
        <button
          type="button"
          class="test-btn !bg-blue-600 hover:!bg-blue-700 !px-4 !py-2 flex items-center space-x-2"
          on:click={confirmEdit}
          disabled={isEditing || editForm.quantity <= 0}
        >
          {#if isEditing}
            <iconify-icon icon="lucide:loader-2" class="text-sm animate-spin"></iconify-icon>
            <span>更新中...</span>
          {:else}
            <iconify-icon icon="lucide:save" class="text-sm"></iconify-icon>
            <span>儲存變更</span>
          {/if}
        </button>
      </div>
    </div>
  </div>
{/if}

