/**
 * 藥物相關型別定義 - 統一資料模型
 * 最後更新: 2025/7/22
 */

// 基礎藥物資訊
export interface Pharmaceutical {
  id: number;
  name: string;
  manufacturer?: string;
  activeIngredient?: string;
  dosageUnit: string;
  dosagePerServing?: number;
  servingUnitName?: string;
  currentPlan?: boolean;
  description?: string;
  createdBy: number;
}

// 可用藥物資訊 (與 Pharmaceutical 相同，但使用 unit 而非 dosageUnit)
export interface AvailablePharmaceutical {
  id: number;
  name: string;
  manufacturer?: string;
  activeIngredient?: string;
  unit: string;
  dosagePerServing?: number;
  servingUnitName?: string;
  currentPlan?: boolean;
  description?: string;
}

// 服藥記錄
export interface PharmaceuticalTakeRecord {
  id: number;
  pharmaceuticalId: number;
  pharmaceuticalName: string;
  dosage: number;
  dosageUnit: string;
  dosagePerServing: number;
  servingUnitName?: string | null;
  takenAt: string; // ISO 日期字串
  notes?: string;
  createdBy: number;
}

// 快速服藥選項
export interface QuickPharmaceuticalOption {
  pharmaceuticalId: number;
  pharmaceuticalName: string;
  dosage: number;
  dosageUnit: string;
  count: number; // 該劑量的總服用次數
  todayCount: number; // 當日服用次數
  lastTakenAt: string; // 最後服用時間
}

// 新增服藥記錄輸入
export interface AddPharmaceuticalTakeInput {
  userId: number;
  pharmaceuticalId: number;
  quantity: number;
  takenAt?: string;
  notes?: string;
}

// 更新服藥記錄輸入
export interface UpdatePharmaceuticalTakeInput {
  id: number;
  quantity?: number;
  takenAt?: string;
  notes?: string;
}

// 服藥統計資訊
export interface PharmaceuticalStats {
  pharmaceuticalId: number;
  pharmaceuticalName: string;
  totalTakes: number;
  todayTakes: number;
  weeklyTakes: number;
  monthlyTakes: number;
  lastTakenAt?: string;
  averageDailyDosage: number;
}

// 服藥提醒設定
export interface PharmaceuticalReminder {
  id: number;
  pharmaceuticalId: number;
  userId: number;
  reminderTimes: string[]; // 提醒時間陣列 (HH:MM 格式)
  isActive: boolean;
  notes?: string;
}

// 劑型枚舉
export enum FormulationType {
  CAPSULE = 'Capsule',
  PILL = 'Pill', 
  INJECTION = 'Injection',
  POWDER = 'Powder',
  SYRUP = 'Syrup',
  PATCH = 'Patch',
  SPRAY = 'Spray',
  TOPICAL = 'Topical'
}

// 服藥頻率枚舉
export enum DosageFrequency {
  ONCE_DAILY = 'once_daily',
  TWICE_DAILY = 'twice_daily',
  THREE_TIMES_DAILY = 'three_times_daily',
  FOUR_TIMES_DAILY = 'four_times_daily',
  AS_NEEDED = 'as_needed',
  CUSTOM = 'custom'
}
