import { writable, derived, get } from 'svelte/store';
import { getCurrentLocalISOString } from './utils/dateHelpers';
import { currentUser } from './auth';
import { queryTakes, queryPharmaceuticals } from './graphql/services';
import type { PharmaceuticalTakeRecord, QuickPharmaceuticalOption } from './types/pharmaceutical';

// 資料儲存
export const pharmaceuticalTakeRecords = writable<PharmaceuticalTakeRecord[]>([]);
export const quickPharmaceuticalOptions = writable<QuickPharmaceuticalOption[]>([]);
export const isLoading = writable<boolean>(false);
export const isLoadingMore = writable<boolean>(false);
export const hasMoreData = writable<boolean>(true);
export const currentPage = writable<number>(0);
export const lastUpdated = writable<Date | null>(null);

// 新增：變更追蹤
export const changedRecordIds = writable<Set<number>>(new Set());
export const changedQuickOptions = writable<Set<number>>(new Set());

// 新增：載入所有藥物清單
export const availablePharmaceuticals = writable<Array<{
  id: number;
  name: string;
  dosageUnit: string;
  dosagePerServing?: number;
  servingUnitName?: string;
  currentPlan?: boolean;
  description?: string;
}>>([]);

// 輔助函數
export function getServingUnitDisplay(servingUnitName?: string | null): string {
  return servingUnitName || '份';
}

export function formatDosageDisplay(absoluteDosage: number, dosagePerServing: number, dosageUnit: string, servingUnitName?: string | null): string {
  const servingUnit = getServingUnitDisplay(servingUnitName);
  const servingCount = dosagePerServing > 0 ? absoluteDosage / dosagePerServing : absoluteDosage;
  return `${servingCount}${servingUnit} 共${absoluteDosage}${dosageUnit}`;
}

export function formatDailyTotalDisplay(count: number, totalServings: number, totalDosage: number, dosageUnit: string, servingUnitName?: string | null): string {
  const servingUnit = getServingUnitDisplay(servingUnitName);
  return `服用${count}次，共${totalServings}${servingUnit} ${totalDosage}${dosageUnit}`;
}

// 衍生狀態
export const todayTakeRecords = derived(
  pharmaceuticalTakeRecords,
  ($records) => {
    const today = getCurrentLocalISOString().split('T')[0];
    return $records.filter(record =>
      record.takenAt.startsWith(today)
    );
  }
);

export const recentTakeRecords = derived(
  pharmaceuticalTakeRecords,
  ($records) => {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    return $records.filter(record =>
      new Date(record.takenAt) >= thirtyDaysAgo
    );
  }
);

// GraphQL 查詢現在通過 urql 服務處理

// 輔助函數：深度比對兩個記錄陣列
function compareRecords(oldRecords: PharmaceuticalTakeRecord[], newRecords: PharmaceuticalTakeRecord[]): {
  added: PharmaceuticalTakeRecord[];
  updated: PharmaceuticalTakeRecord[];
  removed: number[];
} {
  const oldMap = new Map(oldRecords.map(r => [r.id, r]));
  const newMap = new Map(newRecords.map(r => [r.id, r]));
  
  const added: PharmaceuticalTakeRecord[] = [];
  const updated: PharmaceuticalTakeRecord[] = [];
  const removed: number[] = [];

  // 找出新增和更新的記錄
  for (const newRecord of newRecords) {
    const oldRecord = oldMap.get(newRecord.id);
    if (!oldRecord) {
      added.push(newRecord);
    } else if (JSON.stringify(oldRecord) !== JSON.stringify(newRecord)) {
      updated.push(newRecord);
    }
  }

  // 找出已刪除的記錄
  for (const oldId of oldMap.keys()) {
    if (!newMap.has(oldId)) {
      removed.push(oldId);
    }
  }

  return { added, updated, removed };
}

// 輔助函數：深度比對快速選項
function compareQuickOptions(oldOptions: QuickPharmaceuticalOption[], newOptions: QuickPharmaceuticalOption[]): {
  added: QuickPharmaceuticalOption[];
  updated: QuickPharmaceuticalOption[];
  removed: number[];
} {
  const oldMap = new Map(oldOptions.map(o => [o.pharmaceuticalId, o]));
  const newMap = new Map(newOptions.map(o => [o.pharmaceuticalId, o]));
  
  const added: QuickPharmaceuticalOption[] = [];
  const updated: QuickPharmaceuticalOption[] = [];
  const removed: number[] = [];

  // 找出新增和更新的選項
  for (const newOption of newOptions) {
    const oldOption = oldMap.get(newOption.pharmaceuticalId);
    if (!oldOption) {
      added.push(newOption);
    } else if (JSON.stringify(oldOption) !== JSON.stringify(newOption)) {
      updated.push(newOption);
    }
  }

  // 找出已刪除的選項
  for (const oldId of oldMap.keys()) {
    if (!newMap.has(oldId)) {
      removed.push(oldId);
    }
  }

  return { added, updated, removed };
}

// 使用差異更新記錄和快速選項
function updateRecordsWithDiff(newRecords: PharmaceuticalTakeRecord[]): void {
  const currentRecords = get(pharmaceuticalTakeRecords);
  const currentQuickOptions = get(quickPharmaceuticalOptions);
  
  // 比對服藥記錄
  const recordDiff = compareRecords(currentRecords, newRecords);
  
  // 追蹤變更的記錄 ID
  const changedIds = new Set<number>();
  recordDiff.added.forEach(r => changedIds.add(r.id));
  recordDiff.updated.forEach(r => changedIds.add(r.id));
  
  // 更新記錄
  pharmaceuticalTakeRecords.set(newRecords);
  
  // 重新生成快速選項
  const newQuickOptions = generateQuickPharmaceuticalOptionsSync(newRecords);
  
  // 比對快速選項
  const optionDiff = compareQuickOptions(currentQuickOptions, newQuickOptions);
  
  // 追蹤變更的快速選項 ID
  const changedOptionIds = new Set<number>();
  optionDiff.added.forEach(o => changedOptionIds.add(o.pharmaceuticalId));
  optionDiff.updated.forEach(o => changedOptionIds.add(o.pharmaceuticalId));
  
  // 更新快速選項
  quickPharmaceuticalOptions.set(newQuickOptions);
  
  // 設置變更追蹤 (用於動畫效果)
  changedRecordIds.set(changedIds);
  changedQuickOptions.set(changedOptionIds);
  
  // 在 3 秒後清除變更追蹤
  setTimeout(() => {
    changedRecordIds.set(new Set());
    changedQuickOptions.set(new Set());
  }, 3000);
}

// 載入服藥紀錄
export async function loadPharmaceuticalTakeRecords(): Promise<void> {
  try {
    isLoading.set(true);
    resetPagination();

    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const fromTime = thirtyDaysAgo.toISOString();
    
    // 分批載入所有30天內的資料
    const allRecords: PharmaceuticalTakeRecord[] = [];
    let offset = 0;
    const batchSize = 50; // 每批載入50筆
    let hasMore = true;

    while (hasMore) {
      const takes = await queryTakes({ 
        fromTime: fromTime,
        offset: offset,
        limit: batchSize
      });
      
      if (takes.length === 0) {
        hasMore = false;
        break;
      }

      const batchRecords: PharmaceuticalTakeRecord[] = takes.map((take) => ({
        id: take.id,
        pharmaceuticalId: take.pharmaceutical!.id,
        pharmaceuticalName: take.pharmaceutical!.name,
        dosage: take.quantity,
        dosageUnit: take.pharmaceutical!.dosageUnit,
        dosagePerServing: take.pharmaceutical!.dosagePerServing,
        servingUnitName: take.pharmaceutical!.servingUnitName,
        takenAt: take.takenAt,
        notes: take.notes || '',
        createdBy: take.pharmaceutical!.createdBy
      }));

      allRecords.push(...batchRecords);
      offset += batchSize;

      // 如果這一批的資料數量少於 batchSize，說明沒有更多資料了
      if (takes.length < batchSize) {
        hasMore = false;
      }
    }

    // 按時間降序排序
    allRecords.sort((a, b) => new Date(b.takenAt).getTime() - new Date(a.takenAt).getTime());

    // 深度比對並更新差異
    updateRecordsWithDiff(allRecords);
    lastUpdated.set(new Date());

  } catch (error) {
    console.error('載入服藥紀錄失敗:', error);
    // 設置空陣列避免 UI 錯誤
    pharmaceuticalTakeRecords.set([]);
    quickPharmaceuticalOptions.set([]);
    throw error;
  } finally {
    isLoading.set(false);
  }
}

// 生成快速服藥選項（同步版本）
export function generateQuickPharmaceuticalOptionsSync(records: PharmaceuticalTakeRecord[]): QuickPharmaceuticalOption[] {
  console.log('=== 快速選項生成除錯 ===');
  
  // 取得當前的藥物清單以檢查 current_plan 狀態
  const pharmaceuticals = get(availablePharmaceuticals);
  console.log('所有藥物清單:', pharmaceuticals);
  
  const currentPlanPharmaceuticals = new Set(
    pharmaceuticals.filter(p => p.currentPlan).map(p => p.id)
  );
  console.log('治療中的藥物ID集合:', Array.from(currentPlanPharmaceuticals));
  
  console.log('輸入的服藥記錄:', records);
  
  // 過濾只包含治療中的藥物（後端已經確保只返回當前使用者的記錄）
  const authorizedRecords = records.filter(record => 
    currentPlanPharmaceuticals.has(record.pharmaceuticalId)
  );
  
  console.log('授權的服藥記錄 (治療中藥物):', authorizedRecords);
  const pharmaceuticalGroups = new Map<string, {
    pharmaceuticalId: number;
    pharmaceuticalName: string;
    dosage: number;
    dosageUnit: string;
    records: PharmaceuticalTakeRecord[];
  }>();

  authorizedRecords.forEach(record => {
    const key = `${record.pharmaceuticalId}-${record.dosage}`;

    if (!pharmaceuticalGroups.has(key)) {
      pharmaceuticalGroups.set(key, {
        pharmaceuticalId: record.pharmaceuticalId,
        pharmaceuticalName: record.pharmaceuticalName,
        dosage: record.dosage,
        dosageUnit: record.dosageUnit,
        records: []
      });
    }

    pharmaceuticalGroups.get(key)!.records.push(record);
  });

  const pharmaceuticalMap = new Map<number, {
    pharmaceuticalId: number;
    pharmaceuticalName: string;
    bestDosage: number;
    dosageUnit: string;
    maxCount: number;
    todayCount: number;
    lastTakenAt: string;
  }>();

  for (const [key, group] of pharmaceuticalGroups) {
    const count = group.records.length;
    
    // 計算當日服用次數
    const today = new Date().toDateString();
    const todayCount = group.records.filter(record => {
      const recordDate = new Date(record.takenAt).toDateString();
      return recordDate === today;
    }).length;
    
    const lastTakenAt = group.records
      .sort((a, b) => new Date(b.takenAt).getTime() - new Date(a.takenAt).getTime())[0]
      .takenAt;

    const pharmaceuticalId = group.pharmaceuticalId;

    if (!pharmaceuticalMap.has(pharmaceuticalId) ||
        count > pharmaceuticalMap.get(pharmaceuticalId)!.maxCount) {
      pharmaceuticalMap.set(pharmaceuticalId, {
        pharmaceuticalId: group.pharmaceuticalId,
        pharmaceuticalName: group.pharmaceuticalName,
        bestDosage: group.dosage,
        dosageUnit: group.dosageUnit,
        maxCount: count,
        todayCount: todayCount,
        lastTakenAt
      });
    }
  }

  const options: QuickPharmaceuticalOption[] = Array.from(pharmaceuticalMap.values())
    .map(item => ({
      pharmaceuticalId: item.pharmaceuticalId,
      pharmaceuticalName: item.pharmaceuticalName,
      dosage: item.bestDosage,
      dosageUnit: item.dosageUnit,
      count: item.maxCount,
      todayCount: item.todayCount,
      lastTakenAt: item.lastTakenAt
    }))
    .sort((a, b) => new Date(b.lastTakenAt).getTime() - new Date(a.lastTakenAt).getTime());

  console.log('最終生成的快速選項:', options);
  console.log('=== 快速選項生成結束 ===');

  return options;
}

// 生成快速服藥選項（原版本，用於向後相容）
function generateQuickPharmaceuticalOptions(records: PharmaceuticalTakeRecord[]): void {
  const options = generateQuickPharmaceuticalOptionsSync(records);
  quickPharmaceuticalOptions.set(options);
}

// 刷新資料
export async function refreshPharmaceuticalData(): Promise<void> {
  await loadPharmaceuticalTakeRecords();
}

// 定時刷新設定
let refreshInterval: number | null = null;

export function startAutoRefresh(): void {
  if (refreshInterval) {
    clearInterval(refreshInterval);
  }

  refreshInterval = setInterval(() => {
    loadPharmaceuticalTakeRecords().catch(error => {
      console.error('定時刷新服藥紀錄失敗:', error);
    });
  }, 30 * 60 * 1000);
}

export function stopAutoRefresh(): void {
  if (refreshInterval) {
    clearInterval(refreshInterval);
    refreshInterval = null;
  }
}

// 服藥紀錄變更回調（使用差異更新）
export async function onPharmaceuticalTakeRecordChanged(): Promise<void> {
  try {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const fromTime = thirtyDaysAgo.toISOString();
    
    // 分批載入所有30天內的資料
    const allRecords: PharmaceuticalTakeRecord[] = [];
    let offset = 0;
    const batchSize = 50;
    let hasMore = true;

    while (hasMore) {
      const takes = await queryTakes({ 
        fromTime: fromTime,
        offset: offset,
        limit: batchSize
      });
      
      if (takes.length === 0) {
        hasMore = false;
        break;
      }

      const batchRecords: PharmaceuticalTakeRecord[] = takes.map((take) => ({
        id: take.id,
        pharmaceuticalId: take.pharmaceutical!.id,
        pharmaceuticalName: take.pharmaceutical!.name,
        dosage: take.quantity,
        dosageUnit: take.pharmaceutical!.dosageUnit,
        dosagePerServing: take.pharmaceutical!.dosagePerServing,
        servingUnitName: take.pharmaceutical!.servingUnitName,
        takenAt: take.takenAt,
        notes: take.notes || '',
        createdBy: take.pharmaceutical!.createdBy
      }));

      allRecords.push(...batchRecords);
      offset += batchSize;

      if (takes.length < batchSize) {
        hasMore = false;
      }
    }

    // 按時間降序排序
    allRecords.sort((a, b) => new Date(b.takenAt).getTime() - new Date(a.takenAt).getTime());

    // 使用差異更新
    updateRecordsWithDiff(allRecords);
    lastUpdated.set(new Date());

  } catch (error) {
    console.error('刷新服藥紀錄失敗:', error);
    throw error;
  }
}

// 載入更多歷史紀錄
export async function loadMorePharmaceuticalTakeRecords(): Promise<boolean> {
  try {
    isLoadingMore.set(true);

    let page: number;
    currentPage.update(p => {
      page = p + 1;
      return page;
    });

    const daysToGoBack = 30 + (page! * 30);
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - daysToGoBack);

    const endDate = new Date();
    endDate.setDate(endDate.getDate() - (30 + ((page! - 1) * 30)));

    const takes = await queryTakes({
      fromTime: startDate.toISOString(),
      toTime: endDate.toISOString()
    });

    const newRecords: PharmaceuticalTakeRecord[] = takes.map((take) => ({
      id: take.id,
      pharmaceuticalId: take.pharmaceutical!.id,
      pharmaceuticalName: take.pharmaceutical!.name,
      dosage: take.quantity,
      dosageUnit: take.pharmaceutical!.dosageUnit,
      dosagePerServing: take.pharmaceutical!.dosagePerServing,
      servingUnitName: take.pharmaceutical!.servingUnitName,
      takenAt: take.takenAt,
      notes: take.notes || '',
      createdBy: take.pharmaceutical!.createdBy
    }));

    if (newRecords.length === 0) {
      hasMoreData.set(false);
      return false;
    }

    pharmaceuticalTakeRecords.update(existing => {
      const combined = [...existing, ...newRecords];
      const unique = combined.filter((record, index, self) =>
        index === self.findIndex(r => r.id === record.id)
      );
      return unique.sort((a, b) => new Date(b.takenAt).getTime() - new Date(a.takenAt).getTime());
    });

    return true;
  } catch (error) {
    console.error('載入更多服藥紀錄失敗:', error);
    throw error;
  } finally {
    isLoadingMore.set(false);
  }
}

// 重置分頁狀態
export function resetPagination(): void {
  currentPage.set(0);
  hasMoreData.set(true);
}

// 清理資料
export function clearPharmaceuticalData(): void {
  pharmaceuticalTakeRecords.set([]);
  quickPharmaceuticalOptions.set([]);
  lastUpdated.set(null);
  resetPagination();
  stopAutoRefresh();
}

// 新增：載入所有藥物清單
export async function loadAvailablePharmaceuticals(): Promise<void> {
  try {
    const pharmaceuticals = await queryPharmaceuticals();
    
    // 轉換格式以符合現有介面
    const legacyFormat = pharmaceuticals.map((pharm) => ({
      id: pharm.id,
      name: pharm.name,
      dosageUnit: pharm.dosageUnit,
      dosagePerServing: pharm.dosagePerServing,
      servingUnitName: pharm.servingUnitName,
      currentPlan: pharm.currentPlan,
      description: pharm.description
    }));
    
    availablePharmaceuticals.set(legacyFormat);
  } catch (error) {
    console.error('載入藥物清單失敗:', error);
    throw error;
  }
}
