<script lang="ts">
  import { pharmaceuticalTakeRecords, availablePharmaceuticals, type QuickPharmaceuticalOption, changedQuickOptions, formatDailyTotalDisplay, getServingUnitDisplay } from '$lib';
  import { quickAddPharmaceutical, checkRecentTake } from '$lib/pharmaceuticalActions';
  import { createEventDispatcher } from 'svelte';

  export let option: QuickPharmaceuticalOption;
  export let expandable: boolean = true;

  let isExpanded = false;
  let isAdding = false;
  let showWarning = false;
  let warningMinutesAgo = 0;
  let proceedWithWarning = false;
  
  const dispatch = createEventDispatcher<{
    added: {
      pharmaceuticalId: number;
      pharmaceuticalName: string;
      dosage: number;
      unit: string;
    };
    error: {
      error: string;
    };
  }>();

  // 獲取藥物詳細資訊
  $: pharmaceutical = $availablePharmaceuticals.find(p => p.id === option.pharmaceuticalId);

  // 計算今日該藥物的統計數據
  $: todayRecords = $pharmaceuticalTakeRecords
    .filter(r => {
      const recordDate = new Date(r.takenAt).toDateString();
      const today = new Date().toDateString();
      return recordDate === today && r.pharmaceuticalId === option.pharmaceuticalId;
    });

  $: todayTotalCount = todayRecords.length;
  $: todayTotalDosage = todayRecords.reduce((total, r) => total + r.dosage, 0);
  $: todayTotalServings = pharmaceutical?.dosagePerServing ? todayTotalDosage / pharmaceutical.dosagePerServing : todayTotalDosage;

  // 獲取今日該藥物的服用時間
  $: todayTimes = todayRecords
    .map(r => new Date(r.takenAt))
    .sort((a, b) => a.getTime() - b.getTime());

  // 格式化時間顯示
  const formatTime = (date: Date) => {
    return date.toLocaleString('zh-TW', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  function toggleExpand(event: Event) {
    event.stopPropagation();
    // 更新使用者活動時間
    if (typeof window !== 'undefined') {
      import('$lib/auth').then(({ updateUserActivity }) => {
        updateUserActivity();
      }).catch(err => console.warn('無法更新使用者活動時間:', err));
    }
    if (expandable) {
      isExpanded = !isExpanded;
    }
  }

  async function handleQuickAdd() {
    if (isAdding) return;

    try {
      isAdding = true;
      
      // 更新使用者活動時間
      if (typeof window !== 'undefined') {
        import('$lib/auth').then(({ updateUserActivity }) => {
          updateUserActivity();
        }).catch(err => console.warn('無法更新使用者活動時間:', err));
      }
      
      // 檢查30分鐘內是否服用過
      if (!proceedWithWarning) {
        const recentCheck = await checkRecentTake(option.pharmaceuticalId);
        if (recentCheck.hasRecentTake) {
          showWarning = true;
          warningMinutesAgo = recentCheck.minutesAgo || 0;
          isAdding = false;
          return;
        }
      }
      
      const result = await quickAddPharmaceutical(
        option.pharmaceuticalId,
        option.dosage,
        '快速服用'
      );

      // 重置狀態
      showWarning = false;
      proceedWithWarning = false;

      dispatch('added', {
        pharmaceuticalId: option.pharmaceuticalId,
        pharmaceuticalName: option.pharmaceuticalName,
        dosage: option.dosage,
        unit: option.dosageUnit
      });
    } catch (error) {
      console.error('快速服藥失敗:', error);
      dispatch('error', { error: error instanceof Error ? error.message : String(error) });
    } finally {
      isAdding = false;
    }
  }

  function confirmProceed() {
    // 更新使用者活動時間
    if (typeof window !== 'undefined') {
      import('$lib/auth').then(({ updateUserActivity }) => {
        updateUserActivity();
      }).catch(err => console.warn('無法更新使用者活動時間:', err));
    }
    proceedWithWarning = true;
    showWarning = false;
    handleQuickAdd();
  }

  function cancelWarning() {
    // 更新使用者活動時間
    if (typeof window !== 'undefined') {
      import('$lib/auth').then(({ updateUserActivity }) => {
        updateUserActivity();
      }).catch(err => console.warn('無法更新使用者活動時間:', err));
    }
    showWarning = false;
    proceedWithWarning = false;
  }

  // 檢查是否為最近變更的項目
  $: isRecentlyChanged = $changedQuickOptions.has(option.pharmaceuticalId);
</script>

<div 
  class="rounded-lg border border-gray-200 dark:border-gray-700 hover:border-green-300 dark:hover:border-green-600 transition-all duration-300 cursor-pointer {isRecentlyChanged ? 'bg-green-50 dark:bg-green-900/30 border-green-300 dark:border-green-600' : 'hover:bg-green-50/50 dark:hover:bg-green-900/10'}"
  on:click={handleQuickAdd}
  role="button"
  tabindex="0"
  on:keydown={(e) => e.key === 'Enter' && handleQuickAdd()}
>
  <!-- 主要資訊 (橫向顯示) -->
  <div class="flex items-center justify-between py-3 px-4">
    <div class="flex items-center space-x-3 flex-1">
      <!-- 藥物名稱 -->
      <div class="flex-1 min-w-0">
        <h3 class="font-medium text-gray-900 dark:text-gray-100 truncate">
          {option.pharmaceuticalName}
        </h3>
        <p class="text-xs text-gray-500 dark:text-gray-400">
          {#if todayTotalCount > 0 && pharmaceutical}
            {formatDailyTotalDisplay(todayTotalCount, todayTotalServings, todayTotalDosage, option.dosageUnit, pharmaceutical.servingUnitName)}
          {:else}
            今日尚未服用
          {/if}
        </p>
      </div>

      <!-- 劑量 -->
      <div class="text-right mr-3">
        <span class="font-medium text-green-600 dark:text-green-400">
          {#if pharmaceutical?.dosagePerServing}
            {@const servingCount = option.dosage / pharmaceutical.dosagePerServing}
            {@const servingUnit = getServingUnitDisplay(pharmaceutical.servingUnitName)}
            {servingCount}{servingUnit}（{option.dosage} {option.dosageUnit}）
          {:else}
            {option.dosage} {option.dosageUnit}
          {/if}
        </span>
      </div>
    </div>

    <!-- 展開按鈕 -->
    {#if expandable}
      <button
        class="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-200 {isExpanded ? 'rotate-180' : ''}"
        on:click={toggleExpand}
        aria-label="{isExpanded ? '收合詳細資訊' : '展開詳細資訊'}"
      >
        <iconify-icon icon="lucide:chevron-down" class="text-sm text-gray-600 dark:text-gray-300"></iconify-icon>
      </button>
    {/if}
  </div>

  <!-- 展開內容 -->
  {#if isExpanded}
    <div class="border-t border-gray-200 dark:border-gray-700 bg-transparent px-4 py-3 expand-enter rounded-b-lg">
      <!-- 當日服用記錄 -->
      {#if todayTimes.length > 0}
        <div class="space-y-2">
          <div class="flex flex-wrap gap-2">
            {#each todayTimes as time, index}
              <div class="bg-gray-50/80 dark:bg-gray-700/80 backdrop-blur-sm rounded-lg px-3 py-2 border border-gray-200/50 dark:border-gray-600/50 flex items-center space-x-2">
                <iconify-icon icon="lucide:clock" class="text-sm text-blue-500"></iconify-icon>
                <span class="text-sm font-medium text-gray-800 dark:text-gray-200">
                  {formatTime(time)}
                </span>
                <span class="text-xs text-gray-500 dark:text-gray-400">
                  {option.dosage} {option.dosageUnit}
                </span>
              </div>
            {/each}
          </div>
          <!-- 總劑量 -->
          <div class="mt-3 pt-2 border-t border-gray-200/50 dark:border-gray-600/50">
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-300">今日總劑量</span>
              <span class="font-semibold text-green-600 dark:text-green-400">{todayTotalDosage} {option.dosageUnit}</span>
            </div>
          </div>
        </div>
      {:else}
        <div class="flex items-center justify-center py-4">
          <div class="text-center">
            <iconify-icon icon="lucide:clock" class="text-2xl text-gray-400 mb-2"></iconify-icon>
            <span class="text-sm text-gray-500 dark:text-gray-400 block">
              今日尚未服用
            </span>
          </div>
        </div>
      {/if}
    </div>
  {/if}
</div>

<!-- 30分鐘警告對話框 -->
{#if showWarning}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 fade-enter" role="dialog" aria-labelledby="warning-title">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 p-6 fade-enter">
      <div class="flex items-center space-x-3 mb-4">
        <div class="flex-shrink-0">
          <iconify-icon icon="lucide:alert-triangle" class="text-2xl text-yellow-500"></iconify-icon>
        </div>
        <div>
          <h3 id="warning-title" class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            重複服藥警告
          </h3>
        </div>
      </div>
      
      <div class="mb-6">
        <p class="text-gray-600 dark:text-gray-300 mb-3">
          您在 <strong>{warningMinutesAgo} 分鐘前</strong> 已服用過「<strong>{option.pharmaceuticalName}</strong>」。
        </p>
        <p class="text-sm text-gray-500 dark:text-gray-400">
          建議等待至少30分鐘後再次服用，以確保用藥安全。您確定要繼續嗎？
        </p>
      </div>

      <div class="flex space-x-3 justify-end">
        <button
          type="button"
          class="btn btn-ghost"
          on:click={cancelWarning}
        >
          取消
        </button>
        <button
          type="button"
          class="btn btn-warning"
          on:click={confirmProceed}
        >
          仍要服用
        </button>
      </div>
    </div>
  </div>
{/if}